#include "my_gazebo_interface/gazebo_mecanum_interface.hpp"
#include "std_msgs/msg/float64.hpp"
#include "sensor_msgs/msg/joint_state.hpp"
#include "pluginlib/class_list_macros.hpp"
#include <chrono>
#include <thread>

namespace my_hardware_interface
{

hardware_interface::CallbackReturn GazeboMecanumInterface::on_init(
  const hardware_interface::HardwareInfo & info)
{
  if (hardware_interface::SystemInterface::on_init(info) != hardware_interface::CallbackReturn::SUCCESS)
  {
    return hardware_interface::CallbackReturn::ERROR;
  }

  joint_count_ = info_.joints.size();
  hw_commands_.resize(joint_count_, 0.0);
  hw_positions_.resize(joint_count_, 0.0);
  hw_velocities_.resize(joint_count_, 0.0);
  hw_efforts_.resize(joint_count_, 0.0);
  current_joint_positions_.resize(joint_count_, 0.0);
  current_joint_velocities_.resize(joint_count_, 0.0);
  current_joint_efforts_.resize(joint_count_, 0.0);

  // 创建关节名称到索引的映射
  for (size_t i = 0; i < joint_count_; i++) {
    joint_name_to_index_[info_.joints[i].name] = i;
  }

  // 创建ROS节点用于与Gazebo通信
  node_ = rclcpp::Node::make_shared("gazebo_mecanum_interface");
  
  // 为每个关节创建命令发布器
  for (size_t i = 0; i < joint_count_; i++) {
    std::string topic_name = "/" + info_.joints[i].name + "/cmd_vel";
    joint_cmd_pubs_.push_back(
      node_->create_publisher<std_msgs::msg::Float64>(topic_name, 10)
    );
  }
  
  // 创建关节状态订阅器
  joint_state_sub_ = node_->create_subscription<sensor_msgs::msg::JointState>(
    "/joint_states", 10,
    [this](const sensor_msgs::msg::JointState::SharedPtr msg) {
      // 处理从Gazebo接收的关节状态
      for (size_t i = 0; i < msg->name.size(); i++) {
        auto it = joint_name_to_index_.find(msg->name[i]);
        if (it != joint_name_to_index_.end()) {
          size_t idx = it->second;
          if (i < msg->position.size()) current_joint_positions_[idx] = msg->position[i];
          if (i < msg->velocity.size()) current_joint_velocities_[idx] = msg->velocity[i];
          if (i < msg->effort.size()) current_joint_efforts_[idx] = msg->effort[i];
        }
      }
    });

  // 初始化遥控器状态
  rc_ch1_ = 0.0;
  rc_ch2_ = 0.0;
  rc_ch3_ = 0.0;
  rc_ch4_ = 0.0;
  rc_sw1_ = 0.0;
  rc_sw2_ = 0.0;
  rc_wheel_ = 0.0;
  rc_connected_ = 0.0;

  return hardware_interface::CallbackReturn::SUCCESS;
}

hardware_interface::CallbackReturn GazeboMecanumInterface::on_configure(
  const rclcpp_lifecycle::State & /*previous_state*/)
{
  // 初始化所有值为0
  std::fill(hw_commands_.begin(), hw_commands_.end(), 0.0);
  std::fill(hw_positions_.begin(), hw_positions_.end(), 0.0);
  std::fill(hw_velocities_.begin(), hw_velocities_.end(), 0.0);
  std::fill(hw_efforts_.begin(), hw_efforts_.end(), 0.0);
  
  return hardware_interface::CallbackReturn::SUCCESS;
}

std::vector<hardware_interface::StateInterface> GazeboMecanumInterface::export_state_interfaces()
{
  std::vector<hardware_interface::StateInterface> interfaces;

  for (size_t i = 0; i < joint_count_; i++)
  {
    interfaces.emplace_back(info_.joints[i].name, "position", &hw_positions_[i]);
    interfaces.emplace_back(info_.joints[i].name, "velocity", &hw_velocities_[i]);
    interfaces.emplace_back(info_.joints[i].name, "effort", &hw_efforts_[i]);
  }

  // 添加遥控器状态接口
  for (const auto& [name, ptr] : rc_state_map_) {
    interfaces.emplace_back("rc", name, ptr);
  }

  return interfaces;
}

std::vector<hardware_interface::CommandInterface> GazeboMecanumInterface::export_command_interfaces()
{
  std::vector<hardware_interface::CommandInterface> interfaces;

  for (size_t i = 0; i < joint_count_; i++)
  {
    // 支持位置和速度命令接口
    for (const auto& command_interface : info_.joints[i].command_interfaces) {
      if (command_interface.name == "position") {
        interfaces.emplace_back(info_.joints[i].name, command_interface.name, &hw_commands_[i]);
      } else if (command_interface.name == "velocity") {
        interfaces.emplace_back(info_.joints[i].name, command_interface.name, &hw_commands_[i]);
      }
    }
  }

  return interfaces;
}

hardware_interface::CallbackReturn GazeboMecanumInterface::on_activate(
  const rclcpp_lifecycle::State & /*previous_state*/)
{
  // 启动ROS节点线程
  std::thread([this]() { rclcpp::spin(node_); }).detach();
  
  // 等待一段时间确保Gazebo连接
  std::this_thread::sleep_for(std::chrono::milliseconds(500));
  
  return hardware_interface::CallbackReturn::SUCCESS;
}

hardware_interface::CallbackReturn GazeboMecanumInterface::on_deactivate(
  const rclcpp_lifecycle::State & /*previous_state*/)
{
  // 停止所有关节运动
  for (auto& pub : joint_cmd_pubs_) {
    auto msg = std_msgs::msg::Float64();
    msg.data = 0.0;
    pub->publish(msg);
  }
  
  // 停止ROS节点
  rclcpp::shutdown();
  
  return hardware_interface::CallbackReturn::SUCCESS;
}

hardware_interface::return_type GazeboMecanumInterface::read(
  const rclcpp::Time & /*time*/, const rclcpp::Duration & /*period*/)
{
  // 从Gazebo获取的关节状态更新到硬件接口状态
  for (size_t i = 0; i < joint_count_; i++)
  {
    hw_positions_[i] = current_joint_positions_[i];
    hw_velocities_[i] = current_joint_velocities_[i];
    hw_efforts_[i] = current_joint_efforts_[i];
  }
  
  // 在仿真中模拟遥控器连接
  rc_connected_ = 1.0;
  
  return hardware_interface::return_type::OK;
}

hardware_interface::return_type GazeboMecanumInterface::write(
  const rclcpp::Time & /*time*/, const rclcpp::Duration & /*period*/)
{
  // 为每个关节发布单独的速度命令，保持分布式控制特点
  for (size_t i = 0; i < joint_count_; i++)
  {
    auto msg = std_msgs::msg::Float64();
    msg.data = hw_commands_[i];
    joint_cmd_pubs_[i]->publish(msg);
  }
  
  return hardware_interface::return_type::OK;
}

}  // namespace my_hardware_interface

PLUGINLIB_EXPORT_CLASS(
  my_hardware_interface::GazeboMecanumInterface, hardware_interface::SystemInterface)