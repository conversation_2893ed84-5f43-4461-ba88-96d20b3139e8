#ifndef MY_GAZEBO_INTERFACE__GAZEBO_MECANUM_INTERFACE_HPP_
#define MY_GAZEBO_INTERFACE__GAZEBO_MECANUM_INTERFACE_HPP_

#include "hardware_interface/handle.hpp"
#include "hardware_interface/hardware_info.hpp"
#include "hardware_interface/system_interface.hpp"
#include "hardware_interface/types/hardware_interface_return_values.hpp"
#include "rclcpp/rclcpp.hpp"
#include "rclcpp_lifecycle/node_interfaces/lifecycle_node_interface.hpp"
#include "rclcpp_lifecycle/state.hpp"
#include "sensor_msgs/msg/joint_state.hpp"
#include "std_msgs/msg/float64.hpp"
#include <map>
#include <vector>
#include <string>

namespace my_hardware_interface
{

class GazeboMecanumInterface : public hardware_interface::SystemInterface
{
public:
  RCLCPP_SHARED_PTR_DEFINITIONS(GazeboMecanumInterface)

  hardware_interface::CallbackReturn on_init(
    const hardware_interface::HardwareInfo & info) override;

  hardware_interface::CallbackReturn on_configure(
    const rclcpp_lifecycle::State & previous_state) override;

  std::vector<hardware_interface::StateInterface> export_state_interfaces() override;

  std::vector<hardware_interface::CommandInterface> export_command_interfaces() override;

  hardware_interface::CallbackReturn on_activate(
    const rclcpp_lifecycle::State & previous_state) override;

  hardware_interface::CallbackReturn on_deactivate(
    const rclcpp_lifecycle::State & previous_state) override;

  hardware_interface::return_type read(
    const rclcpp::Time & time, const rclcpp::Duration & period) override;

  hardware_interface::return_type write(
    const rclcpp::Time & time, const rclcpp::Duration & period) override;

private:
  // 仿真专用变量
  std::vector<double> hw_commands_;
  std::vector<double> hw_positions_;
  std::vector<double> hw_velocities_;
  std::vector<double> hw_efforts_;
  
  // 遥控器状态变量
  double rc_ch1_ = 0.0;
  double rc_ch2_ = 0.0;
  double rc_ch3_ = 0.0;
  double rc_ch4_ = 0.0;
  double rc_sw1_ = 0.0;
  double rc_sw2_ = 0.0;
  double rc_wheel_ = 0.0;
  double rc_connected_ = 0.0;
  
  // 状态映射
  std::map<std::string, double*> rc_state_map_ = {
    {"ch1", &rc_ch1_}, {"ch2", &rc_ch2_}, {"ch3", &rc_ch3_}, {"ch4", &rc_ch4_},
    {"sw1", &rc_sw1_}, {"sw2", &rc_sw2_}, {"wheel", &rc_wheel_}, {"connected", &rc_connected_}
  };
  
  size_t joint_count_{0};
  
  // 用于与Gazebo通信的节点
  rclcpp::Node::SharedPtr node_;
  
  // 发布器 - 为每个关节发布命令
  std::vector<rclcpp::Publisher<std_msgs::msg::Float64>::SharedPtr> joint_cmd_pubs_;
  
  // 订阅器 - 接收关节状态
  rclcpp::Subscription<sensor_msgs::msg::JointState>::SharedPtr joint_state_sub_;
  
  // 存储从Gazebo接收的关节状态
  std::vector<double> current_joint_positions_;
  std::vector<double> current_joint_velocities_;
  std::vector<double> current_joint_efforts_;
  
  // 关节名称映射
  std::map<std::string, size_t> joint_name_to_index_;
};

}  // namespace my_hardware_interface

#endif  // MY_GAZEBO_INTERFACE__GAZEBO_MECANUM_INTERFACE_HPP_