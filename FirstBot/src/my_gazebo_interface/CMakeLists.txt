cmake_minimum_required(VERSION 3.8)
project(my_gazebo_interface)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(hardware_interface REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_lifecycle REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(pluginlib REQUIRED)
find_package(controller_interface REQUIRED)
find_package(gazebo_ros2_control REQUIRED)

include_directories(include)

add_library(gazebo_mecanum_interface  SHARED
  src/gazebo_mecanum_interface.cpp)

ament_target_dependencies(gazebo_mecanum_interface
    controller_interface
    gazebo_ros2_control
    hardware_interface
    pluginlib
    rclcpp
    rclcpp_lifecycle
    sensor_msgs
    std_msgs)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

install(TARGETS gazebo_mecanum_interface
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

install(DIRECTORY include/
  DESTINATION include
)

install(FILES hardware_interface_plugin_description.xml
  DESTINATION share/${PROJECT_NAME}
)

pluginlib_export_plugin_description_file(hardware_interface hardware_interface_plugin_description.xml)

ament_export_dependencies(
  hardware_interface
  rclcpp
  rclcpp_lifecycle
  sensor_msgs
  std_msgs
  pluginlib
  controller_interface
)

ament_export_include_directories(include)
ament_export_libraries(gazebo_mecanum_interface)

ament_package()
