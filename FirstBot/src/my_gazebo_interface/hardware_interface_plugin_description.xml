<?xml version="1.0"?>
<library path="my_gazebo_interface">
  <class name="my_gazebo_interface/GazeboMecanumInterface" 
         type="my_gazebo_interface::GazeboMecanumInterface" 
         base_class_type="hardware_interface::SystemInterface">
    <description>
      Gazebo hardware interface for mecanum wheel robot simulation.
      This interface provides distributed control of each wheel joint
      and simulates remote controller states for testing purposes.
    </description>
  </class>
</library>

