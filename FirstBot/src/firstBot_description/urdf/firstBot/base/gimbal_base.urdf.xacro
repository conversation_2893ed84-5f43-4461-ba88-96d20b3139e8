<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
   <xacro:include filename="$(find firstBot_description)/urdf/firstBot/common_inertia.xacro" />
    <xacro:macro name="gimbal_base_xacro">
    <!-- 可以写base_footprint-->
        <link name="gimbal_base_link">
            <visual>
               <origin xyz="6.9389092391875E-05 -0.000615759418149342 -0.0497615088195115" rpy="0 0 0" />
                <geometry>
                    <mesh filename="package://firstBot.urdf/meshes/gimbal_base_link.STL" />
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </visual>

            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                
                <!-- ################设置参数################## -->
                <geometry>
                    <box size="0.0 0.0 0.0"/>
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </collision>

            
            <!-- 空处参数需要填写 -->
            <xacro:cylinder_inertia m="8.40336595428255" r="0.59825" h="2.7402"/>

            <joint name="gimbal_base_joint" type="fixed">
                <origin xyz="-0.0014015 -0.0018662 -0.050192" rpy="3.1416 0 0" />
                <parent link="base_link" />
                <child link="gimbal_base_link" />
                <axis xyz="0 0 0" />
            </joint>
        </link>
    </xacro:macro>
</robot>


<!-- <mass value="0.299530230563647" /> -->