<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

<!-- 该文件是给部件设置惯性，在惯性上面为了简单计算，会将部件简化成长方体 圆柱体 或是球体 -->
<!-- 就是他会有两个方向上的视觉，一个是我们看到的就是写的模型，比较精美；另一个就是在物理上看的，就是惯性 -->


    <!-- 矩形体   m质量   w h d 长宽高 -->
    <xacro:macro name="box_inertia" params="m w h d">
        <inertial>
            <mass value="${m}" />
            <inertia ixx="${(m/12) * (h*h + d*d)}" ixy="0.0" ixz="0.0" iyy="${(m/12) * (w*w + d*d)}" iyz="0.0" izz="${(m/12) * (w*w + h*h)}" />
        </inertial>
    </xacro:macro>


    <!-- 圆柱 m质量 r底面半径 h高 -->
    <xacro:macro name="cylinder_inertia" params="m r h">
        <inertial>
            <mass value="${m}" />
            <inertia ixx="${(m/12) * (3*r*r + h*h)}" ixy="0" ixz="0" iyy="${(m/12) * (3*r*r + h*h)}" iyz="0" izz="${(m/2) * (r*r)}" />
        </inertial>
    </xacro:macro>


    <!-- 球体 m质量 r半径 -->
    <xacro:macro name="sphere_inertia" params="m r">
        <inertial>
            <mass value="${m}" />
            <inertia ixx="${(2/5) * m * (r*r)}" ixy="0.0" ixz="0.0" iyy="${(2/5) * m * (r*r)}" iyz="0.0" izz="${(2/5) * m * (r*r)}" />
        </inertial>
    </xacro:macro>

</robot>