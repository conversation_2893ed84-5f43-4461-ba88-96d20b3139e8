<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:macro name="gimbal_rotor_xacro" params=" "/>
    <link name="gimbal_rotor_link">
      <visual>
        <origin
        xyz="-0.00162517623050926 0.00152859873744088 -0.124952435067618"
        rpy="0 0 0" />
        <geometry>
          <mesh filename="package://firstBot_description/meshes/pitch_Link.STL" />
        </geometry>
        <material name="">
          <color rgba="0 0 0 0.8" />
        </material>
      </visual>

      <collision>
        <origin
          xyz="-0.000660825931109735 0.000125747113190827 0.0368612940049887"
          rpy="0 0 0" />

        <geometry>
          <box size="0.0 0.0 0.0"/>
        </geometry>

      </collision>

      <!-- 空处参数需要填写 w h d-->
      <xacro:box_inertia m="0.232186075150672" r="" h=""/>
    
      <joint name="gimbal_rotor_joint" type="continuous">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="gimbal_base_link" />
        <child link="gimbal_rotor_link"  />
        <axis xyz="0 0 -1" />
      </joint>

    </link>
  </xacro:macro>
</robot>

<!-- 
    <mass value="0.931107292406977" /> 
     <mesh filename="package://firstBot.urdf/meshes/gimbal_rotor_link.STL" />



-->
    