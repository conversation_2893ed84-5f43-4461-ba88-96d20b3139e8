<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
   <xacro:include filename="$(find firstBot_description)/urdf/firstBot/common_inertia.xacro" />
    <xacro:macro name="gimbal_BarrelWithDials_xacro">
    <!-- 可以写base_footprint-->
        <link name="gimbal_BarrelWithDials_link">
            <visual>
               <origin xyz="0.00940344784907775 0.0101347217337836 0.0864175408404878" rpy="0 0 0" />
                <geometry>
                    <mesh ffilename="package://firstBot.urdf/meshes/gimbal_BarrelWithDials_link.STL" />
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </visual>

            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                
                <!-- ################设置参数################## -->
                <geometry>
                    <box size="0.0 0.0 0.0"/>
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </collision>

            
            <!-- 空处参数需要填写 -->
            <xacro:cylinder_inertia m="8.40336595428255" r="" h=""/>

            <joint  name="gimbal_BarrelWithDials_joint" type="revolute">
                <origin xyz="0.04008 0 -0.011014" rpy="0 0 0"/>
                <parent link="gimbal_pitch_adapter_link"/>
                <child link="gimbal_BarrelWithDials_link"/>
                <axis xyz="0.0071368 0 -0.99997" />
            </joint>
        </link>
    </xacro:macro>
</robot>


<!--   <mass value="1.57420276453946"/> -->