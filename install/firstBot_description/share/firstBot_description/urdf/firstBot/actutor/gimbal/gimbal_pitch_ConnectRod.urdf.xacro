<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
   <xacro:include filename="$(find firstBot_description)/urdf/firstBot/common_inertia.xacro" />
    <xacro:macro name="gimbal_pitch_ConnectingRod_xacro">
    <!-- 可以写base_footprint-->
        <link name="gimbal_pitch_ConnectingRod_link">
            <visual>
               <origin xyz="-2.3218063625971E-05 0.00325319769330487 0.00999953526240172" rpy="0 0 0" />
                <geometry>
                    <mesh filename="package://firstBot.urdf/meshes/gimbal_pitch_ConnectingRod_link.STL" />
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </visual>

            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                
                <!-- ################设置参数################## -->
                <geometry>
                    <box size="0.0 0.0 0.0"/>
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </collision>

            
            <!-- 空处参数需要填写 -->
            <xacro:cylinder_inertia m="8.40336595428255" r="" h=""/>

            <joint  name="gimbal_pitch_ConnectingRod_joint" type="prismatic">
                <origin xyz="-0.04 0.064188 -0.0083" rpy="1.5708 0.0071369 0" />
                <parent link="gimbal_pitch_SwingArm_link"/>
                <child link="gimbal_pitch_ConnectingRod_link"/>
                <axis xyz="0 0 -1" />
            </joint>
        </link>
    </xacro:macro>
</robot>


<!--  <mass value="0.0135968502473602" /> -->