<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
   <xacro:include filename="$(find firstBot_description)/urdf/firstBot/common_inertia.xacro" />
    <xacro:macro name="gimbal_pitch_adapter_xacro">
    <!-- 可以写base_footprint-->
        <link name="gimbal_pitch_adapter_link">
            <visual>
               <origin xyz="0.0272324195237878 -0.00269569306105633 0.00458282287130116" rpy="0 0 0" />
                <geometry>
                    <mesh filename="package://firstBot.urdf/meshes/gimbal_pitch_adapter_link.STL" />
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </visual>

            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                
                <!-- ################设置参数################## -->
                <geometry>
                    <box size="0.0 0.0 0.0"/>
                </geometry>
                <material name="blue">
                    <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 0.8" />
                </material>
            </collision>

            
            <!-- 空处参数需要填写 -->
            <xacro:cylinder_inertia m="8.40336595428255" r="" h=""/>

            <joint  name="gimbal_pitch_adapter_joint" type="revolute">
                <origin xyz="7.2082E-05 -0.0101 -0.056812"  rpy="-1.5708 0 0" />
                <parent link="gimbal_pitch_ConnectingRod_link"/>
                <child link="gimbal_pitch_adapter_link"/>
                <axis xyz="-0.0071368 0 0.99997" />
            </joint>
        </link>
    </xacro:macro>
</robot>


<!--   <mass value="0.0525879808870972" /> -->