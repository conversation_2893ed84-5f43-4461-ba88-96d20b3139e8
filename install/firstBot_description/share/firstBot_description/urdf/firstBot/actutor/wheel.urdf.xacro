<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
   <xacro:include filename="$(find firstBot_description)/urdf/firstBot/common_inertia.xacro" />
    <xacro:macro name="wheel_xacro" params="m wheel_name xyz_link xyz_joint axis_joint model_wheel">
        <link name="${wheel_name}_link">

            <visual>
                <origin
                xyz="${xyz_link}"
                rpy="0 0 0" />
                <geometry>
                    <mesh filename="${model_wheel}" />
                </geometry>
                <material name="black">
                    <color rgba="0.0 0.0 0.0 0.8"/>
                </material>
            </visual>


            <collision>
                <origin
                xyz="${xyz_link}"
                rpy="0 0 0" />
                <geometry>
                    <cylinder length="0.04" radius="0.032" />
                </geometry>
                <material name="black">
                    <color rgba="0.0 0.0 0.0 0.8"/>
                </material>
            </collision>

            <!-- 空处参数需要填写 -->
            <xacro:cylinder_inertia m="${m}" h="1.3235" r="0.7615" />
        </link>


        <gazebo reference="${wheel_name}_link">
            <mu1 value="20.0" />
            <mu2 value="20.0" />
            <kp value="1000000000.0" />
            <kd value="1.0" />
        </gazebo>


<!-- 这里是否会有origin标签 -->
        <joint name="${wheel_name}_joint" type="continuous">
            <parent link="base_link" />
            <child link="${wheel_name}_link" />
            <origin xyz="${xyz_joint}" />
            <axis xyz="${axis_joint}" />
        </joint>

        
    </xacro:macro>
</robot>



<!-- 
    wheel front right
     <origin
        xyz="0.000140675520085787 -6.22230275293748E-05 -0.00762079512965609"
        rpy="0 0 0" />

    <mass value="0.411484648337879" />

    <mesh  filename="package://firstBot.urdf/meshes/wheel_front_right.STL" />

    <joint
    name="wheel_front_right_joint"
    type="continuous">
    <origin
      xyz="0.18833 0.17052 -0.073501"
      rpy="1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="wheel_front_right" />
    <axis
      xyz="0 0 1" />
  </joint>

 -->


<!-- 
    wheel front left
    <origin
        xyz="0.000169233238429434 7.97019080428507E-07 -0.00444618677374742"
        rpy="0 0 0" />
    <mass
        value="0.385318074291767" />
    <mesh filename="package://firstBot.urdf/meshes/wheel_front_left.STL" />

    <joint
    name="wheel_front_left_joint"
    type="continuous">
    <origin
      xyz="0.18788 -0.17579 -0.054692"
      rpy="-1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="wheel_front_left" />
    <axis
      xyz="0 0 -1" />
  </joint>
        
 -->


 <!-- 
    wheel back left
    <origin
        xyz="-0.000140675568291948 6.22229555559406E-05 -0.00493098330303238"
        rpy="0 0 0" />
    <mass
        value="0.41148464878395" />
    <mesh
          filename="package://firstBot.urdf/meshes/wheel_back_left.STL" />
    <joint
    name="wheel_back_left_joint"
    type="continuous">
    <origin
      xyz="-0.19103 -0.17579 -0.073501"
      rpy="-1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="wheel_back_left" />
    <axis
      xyz="0 0 -1" />
    </joint>

-->

<!-- 
    wheel back right
    <origin
        xyz="-0.000164912912657939 3.80046545385773E-05 0.00444618685437251"
        rpy="0 0 0" />
    <mass
        value="0.385318082719422" />
    <mesh
          filename="package://firstBot.urdf/meshes/wheel_back_right.STL" />

    <joint
    name="wheel_back_right_joint"
    type="continuous">
    <origin
      xyz="-0.19103 0.17321 -0.073501"
      rpy="-1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="wheel_back_right" />
    <axis
      xyz="0 0 1" />
    </joint>

-->