<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="firstBot">
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/base/base.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/base/gimbal_base.urdf.xacro" />

    <!-- 传感器组件 -->

    <!-- 待定 -->
    
    <!-- 执行器组件 -->
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/wheel.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_BarrelWithDials.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_pitch_adapter.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_pitch_base.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_pitch_ConnectRod.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_pitch_SwingArm.urdf.xacro" />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/actuator/gimbal/gimbal_rotor.urdf.xacro" />

    

    <xacro:base_xacro length="0.12" radius="0.1" />
    <xacro:gimbal_base_xacro />
    <!-- 传感器 -->

    <!-- 待定 -->

    <!-- 执行器主动轮+从动轮 -->
    <xacro:wheel_xacro m="0.411484648337879" wheel_name="wheel_front_right" xyz_link="0.000140675520085787 -6.22230275293748E-05 -0.00762079512965609" xyz_joint="0.18833 0.17052 -0.073501" axis_joint="0 0 1" model_wheel="package://firstBot.urdf/meshes/wheel_front_right.STL" />
    <xacro:wheel_xacro m="0.385318074291767" wheel_name="wheel_front_left" xyz_link="0.000169233238429434 7.97019080428507E-07 -0.00444618677374742" xyz_joint="-0.19103 -0.17579 -0.073501" axis_joint="0 0 -1" model_wheel="package://firstBot.urdf/meshes/wheel_front_left.STL" />
    <xacro:wheel_xacro m="0.41148464878395" wheel_name="wheel_back_left" xyz_link="-0.000140675568291948 6.22229555559406E-05 -0.00493098330303238" xyz_joint="0.18833 0.17052 -0.073501" axis_joint="0 0 -1" model_wheel="package://firstBot.urdf/meshes/wheel_back_left.STL" />
    <xacro:wheel_xacro m="0.385318082719422" wheel_name="wheel_back_right" xyz_link="-0.000164912912657939 3.80046545385773E-05 0.00444618685437251" xyz_joint="-0.19103 0.17321 -0.073501" axis_joint="0 0 1" model_wheel="package://firstBot.urdf/meshes/wheel_back_right.STL" />

    <!-- Gazebo 插件 -->

    <!-- 插件待定 -->
    


    <!-- <xacro:gazebo_control_plugin /> -->
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/firstBot.ros2_control.xacro" />
    <xacro:firstBot_ros2_control />
    <xacro:include filename="$(find firstBot_description)/urdf/firstBot/plugins/gazebo_sensor_plugin.xacro" />
    <xacro:gazebo_sensor_plugin />

    
</robot>