<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <xacro:macro name="fishbot_ros2_control">
    <ros2_control name="FishBotGazeboSystem" type="system">
        <hardware>
            <plugin>gazebo_ros2_control/GazeboSystem</plugin>
        </hardware>
        <joint name="left_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-1</param>
                <param name="max">1</param>
            </command_interface>
            <command_interface name="effort">
                <param name="min">-0.1</param>
                <param name="max">0.1</param>
            </command_interface>
            <state_interface name="position" />
            <state_interface name="velocity" />
            <state_interface name="effort" />
        </joint>
        <joint name="right_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-1</param>
                <param name="max">1</param>
            </command_interface>
            <command_interface name="effort">
                <param name="min">-0.1</param>
                <param name="max">0.1</param>
            </command_interface>
            <state_interface name="position" />
            <state_interface name="velocity" />
            <state_interface name="effort" />
        </joint>
    </ros2_control>
    <gazebo>
        <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
            <parameters>$(find fishbot_description)/config/fishbot_ros2_controller.yaml</parameters>
            <ros>
                <remapping>/fishbot_diff_drive_controller/cmd_vel_unstamped:=/cmd_vel</remapping>
                <remapping>/fishbot_diff_drive_controller/odom:=/odom</remapping>
            </ros>
        </plugin>
    </gazebo>
    </xacro:macro>
</robot>