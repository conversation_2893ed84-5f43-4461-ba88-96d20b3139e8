[31mCMake Error at CMakeLists.txt:17 (find_package):
  By not providing "Findgazebo_ros2_control.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "gazebo_ros2_control", but CMake did not find one.

  Could not find a package configuration file provided by
  "gazebo_ros2_control" with any of the following names:

    gazebo_ros2_controlConfig.cmake
    gazebo_ros2_control-config.cmake

  Add the installation prefix of "gazebo_ros2_control" to CMAKE_PREFIX_PATH
  or set "gazebo_ros2_control_DIR" to a directory containing one of the above
  files.  If "gazebo_ros2_control" provides a separate development package or
  SDK, be sure it has been installed.

[0m
