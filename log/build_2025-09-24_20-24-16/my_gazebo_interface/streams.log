[0.010s] Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface
[0.082s] -- The C compiler identification is GNU 13.3.0
[0.135s] -- The CXX compiler identification is GNU 13.3.0
[0.150s] -- Detecting C compiler ABI info
[0.219s] -- Detecting C compiler ABI info - done
[0.224s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.224s] -- Detecting C compile features
[0.225s] -- Detecting C compile features - done
[0.233s] -- Detecting CXX compiler ABI info
[0.307s] -- Detecting CXX compiler ABI info - done
[0.313s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.313s] -- Detecting CXX compile features
[0.313s] -- Detecting CXX compile features - done
[0.325s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.464s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.590s] -- Found hardware_interface: 4.35.0 (/opt/ros/jazzy/share/hardware_interface/cmake)
[0.664s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.683s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.706s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.731s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.943s] -- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
[0.998s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[1.004s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[1.036s] -- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
[1.098s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
[1.100s] -- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
[1.115s] -- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") 
[1.117s] -- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
[1.122s] -- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
[1.131s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.400s] -- Found controller_interface: 4.35.0 (/opt/ros/jazzy/share/controller_interface/cmake)
[1.414s] [31mCMake Error at CMakeLists.txt:17 (find_package):
[1.415s]   By not providing "Findgazebo_ros2_control.cmake" in CMAKE_MODULE_PATH this
[1.415s]   project has asked CMake to find a package configuration file provided by
[1.415s]   "gazebo_ros2_control", but CMake did not find one.
[1.415s] 
[1.415s]   Could not find a package configuration file provided by
[1.415s]   "gazebo_ros2_control" with any of the following names:
[1.415s] 
[1.415s]     gazebo_ros2_controlConfig.cmake
[1.415s]     gazebo_ros2_control-config.cmake
[1.415s] 
[1.415s]   Add the installation prefix of "gazebo_ros2_control" to CMAKE_PREFIX_PATH
[1.415s]   or set "gazebo_ros2_control_DIR" to a directory containing one of the above
[1.415s]   files.  If "gazebo_ros2_control" provides a separate development package or
[1.415s]   SDK, be sure it has been installed.
[1.415s] 
[1.415s] [0m
[1.415s] -- Configuring incomplete, errors occurred!
[1.425s] Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface
