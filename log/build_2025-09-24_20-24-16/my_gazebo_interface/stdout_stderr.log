-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found hardware_interface: 4.35.0 (/opt/ros/jazzy/share/hardware_interface/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
-- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") 
-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found controller_interface: 4.35.0 (/opt/ros/jazzy/share/controller_interface/cmake)
[31mCMake Error at CMakeLists.txt:17 (find_package):
  By not providing "Findgazebo_ros2_control.cmake" in CMAKE_MODULE_PATH this
  project has asked CMake to find a package configuration file provided by
  "gazebo_ros2_control", but CMake did not find one.

  Could not find a package configuration file provided by
  "gazebo_ros2_control" with any of the following names:

    gazebo_ros2_controlConfig.cmake
    gazebo_ros2_control-config.cmake

  Add the installation prefix of "gazebo_ros2_control" to CMAKE_PREFIX_PATH
  or set "gazebo_ros2_control_DIR" to a directory containing one of the above
  files.  If "gazebo_ros2_control" provides a separate development package or
  SDK, be sure it has been installed.

[0m
-- Configuring incomplete, errors occurred!
