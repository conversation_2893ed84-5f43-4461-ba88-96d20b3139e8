[0.211s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.211s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7ff9d8baaab0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ff9d8baa5d0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ff9d8baa5d0>>, mixin_verb=('build',))
[0.273s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.274s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros2_ws/src/FirstBot(1)'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.274s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'ros'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['cmake', 'python']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'cmake'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'python'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extensions ['python_setup_py']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot) by extension 'python_setup_py'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'ros'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['cmake', 'python']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'cmake'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'python'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extensions ['python_setup_py']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src) by extension 'python_setup_py'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extensions ['ignore', 'ignore_ament_install']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extension 'ignore'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extension 'ignore_ament_install'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extensions ['colcon_pkg']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extension 'colcon_pkg'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extensions ['colcon_meta']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extension 'colcon_meta'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extensions ['ros']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/firstBot_description) by extension 'ros'
[0.305s] DEBUG:colcon.colcon_core.package_identification:Package 'FirstBot/src/firstBot_description' with type 'ros.ament_cmake' and name 'firstBot_description'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extensions ['ignore', 'ignore_ament_install']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extension 'ignore'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extension 'ignore_ament_install'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extensions ['colcon_pkg']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extension 'colcon_pkg'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extensions ['colcon_meta']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extension 'colcon_meta'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extensions ['ros']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(FirstBot/src/my_gazebo_interface) by extension 'ros'
[0.306s] DEBUG:colcon.colcon_core.package_identification:Package 'FirstBot/src/my_gazebo_interface' with type 'ros.ament_cmake' and name 'my_gazebo_interface'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.324s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.324s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.335s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 17 installed packages in /home/<USER>/ros2_ws/install
[0.336s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 21 installed packages in /home/<USER>/dev_ws/install
[0.337s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 342 installed packages in /opt/ros/jazzy
[0.338s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'firstBot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.389s] DEBUG:colcon.colcon_core.verb:Building package 'firstBot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description', 'symlink_install': False, 'test_result_base': None}
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'my_gazebo_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.389s] DEBUG:colcon.colcon_core.verb:Building package 'my_gazebo_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface', 'merge_install': False, 'path': '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface', 'symlink_install': False, 'test_result_base': None}
[0.389s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.390s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.390s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description' with build type 'ament_cmake'
[0.390s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description'
[0.392s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.392s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.392s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.396s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface' with build type 'ament_cmake'
[0.396s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface'
[0.397s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.397s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description
[0.406s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface
[1.094s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description
[1.095s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description -- -j32 -l32
[1.131s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description -- -j32 -l32
[1.139s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
[1.152s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(firstBot_description)
[1.152s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
[1.155s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description' for CMake module files
[1.155s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description' for CMake config files
[1.156s] Level 1:colcon.colcon_core.shell:create_environment_hook('firstBot_description', 'cmake_prefix_path')
[1.156s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.ps1'
[1.157s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.dsv'
[1.157s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.sh'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/bin'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/lib/pkgconfig/firstBot_description.pc'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/lib/python3.12/site-packages'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/bin'
[1.158s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.ps1'
[1.159s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.dsv'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.sh'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.bash'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.zsh'
[1.160s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/colcon-core/packages/firstBot_description)
[1.161s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(firstBot_description)
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description' for CMake module files
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description' for CMake config files
[1.162s] Level 1:colcon.colcon_core.shell:create_environment_hook('firstBot_description', 'cmake_prefix_path')
[1.162s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.ps1'
[1.162s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.dsv'
[1.162s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/hook/cmake_prefix_path.sh'
[1.162s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/bin'
[1.162s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/lib/pkgconfig/firstBot_description.pc'
[1.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/lib/python3.12/site-packages'
[1.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/bin'
[1.163s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.ps1'
[1.163s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.dsv'
[1.163s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.sh'
[1.163s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.bash'
[1.164s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.zsh'
[1.164s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/colcon-core/packages/firstBot_description)
[1.821s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface
[1.831s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(my_gazebo_interface)
[1.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface' for CMake module files
[1.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface' for CMake config files
[1.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/bin'
[1.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/lib/pkgconfig/my_gazebo_interface.pc'
[1.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/lib/python3.12/site-packages'
[1.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/bin'
[1.832s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/my_gazebo_interface/package.ps1'
[1.832s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/my_gazebo_interface/package.dsv'
[1.833s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/my_gazebo_interface/package.sh'
[1.833s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/my_gazebo_interface/package.bash'
[1.833s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/my_gazebo_interface/package.zsh'
[1.833s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface/share/colcon-core/packages/my_gazebo_interface)
[1.843s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.843s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.843s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[1.843s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.850s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.850s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.851s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.857s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.858s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/local_setup.ps1'
[1.858s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/src/FirstBot(1)/install/_local_setup_util_ps1.py'
[1.859s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/setup.ps1'
[1.861s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/local_setup.sh'
[1.861s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros2_ws/src/FirstBot(1)/install/_local_setup_util_sh.py'
[1.861s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/setup.sh'
[1.863s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/local_setup.bash'
[1.863s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/setup.bash'
[1.864s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/local_setup.zsh'
[1.865s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros2_ws/src/FirstBot(1)/install/setup.zsh'
