[0.000000] (-) TimerEvent: {}
[0.000507] (firstBot_description) JobQueued: {'identifier': 'firstBot_description', 'dependencies': OrderedDict()}
[0.000538] (my_gazebo_interface) JobQueued: {'identifier': 'my_gazebo_interface', 'dependencies': OrderedDict()}
[0.000558] (firstBot_description) JobStarted: {'identifier': 'firstBot_description'}
[0.006520] (my_gazebo_interface) JobStarted: {'identifier': 'my_gazebo_interface'}
[0.010643] (firstBot_description) JobProgress: {'identifier': 'firstBot_description', 'progress': 'cmake'}
[0.011163] (firstBot_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description'], 'cwd': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_shooter_controller/lib:/home/<USER>/ros2_ws/install/tide_hw_interface/lib:/home/<USER>/ros2_ws/install/tide_gimbal_controller/lib:/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/ros2_ws/install/serial/lib:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '22669', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '22948', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '81133', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:19998', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/22900,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/22900', 'INVOCATION_ID': 'e3211993af754f99a549a41cea4dd4dc', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc0a30c27ec781a6.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/28d5bc7512b4dc00f1da7b1e392b8057/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.MIGMD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-23bb6a8821.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.015169] (my_gazebo_interface) JobProgress: {'identifier': 'my_gazebo_interface', 'progress': 'cmake'}
[0.015603] (my_gazebo_interface) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/my_gazebo_interface', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/my_gazebo_interface'], 'cwd': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_shooter_controller/lib:/home/<USER>/ros2_ws/install/tide_hw_interface/lib:/home/<USER>/ros2_ws/install/tide_gimbal_controller/lib:/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/ros2_ws/install/serial/lib:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '22669', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '22948', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '81133', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:19998', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/22900,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/22900', 'INVOCATION_ID': 'e3211993af754f99a549a41cea4dd4dc', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc0a30c27ec781a6.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/28d5bc7512b4dc00f1da7b1e392b8057/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.MIGMD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-23bb6a8821.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/my_gazebo_interface', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.088632] (my_gazebo_interface) StdoutLine: {'line': b'-- The C compiler identification is GNU 13.3.0\n'}
[0.088860] (firstBot_description) StdoutLine: {'line': b'-- The C compiler identification is GNU 13.3.0\n'}
[0.099795] (-) TimerEvent: {}
[0.141731] (my_gazebo_interface) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 13.3.0\n'}
[0.141946] (firstBot_description) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 13.3.0\n'}
[0.156032] (firstBot_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.156166] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.199894] (-) TimerEvent: {}
[0.225323] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.225493] (firstBot_description) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.230322] (my_gazebo_interface) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.230438] (firstBot_description) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.230865] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.230928] (firstBot_description) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.231250] (firstBot_description) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.231339] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.239132] (firstBot_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.239350] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.300005] (-) TimerEvent: {}
[0.313730] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.315224] (firstBot_description) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.319147] (my_gazebo_interface) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.319320] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.319581] (my_gazebo_interface) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.320968] (firstBot_description) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.321200] (firstBot_description) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.321413] (firstBot_description) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.331386] (my_gazebo_interface) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.331488] (firstBot_description) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.400094] (-) TimerEvent: {}
[0.470759] (my_gazebo_interface) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[0.475489] (firstBot_description) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[0.500180] (-) TimerEvent: {}
[0.596339] (firstBot_description) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[0.596798] (my_gazebo_interface) StdoutLine: {'line': b'-- Found hardware_interface: 4.35.0 (/opt/ros/jazzy/share/hardware_interface/cmake)\n'}
[0.600257] (-) TimerEvent: {}
[0.643005] (firstBot_description) StderrLine: {'line': b'WARNING: Package name "firstBot_description" does not follow the naming conventions. It should start with a lower case letter and only contain lower case letters, digits, underscores, and dashes.\n'}
[0.670324] (my_gazebo_interface) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[0.689049] (firstBot_description) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[0.689152] (firstBot_description) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[0.689435] (my_gazebo_interface) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[0.690404] (firstBot_description) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.691515] (firstBot_description) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[0.694164] (firstBot_description) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.696458] (firstBot_description) StdoutLine: {'line': b'-- Configuring done (0.7s)\n'}
[0.698626] (firstBot_description) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[0.700168] (firstBot_description) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description\n'}
[0.700333] (-) TimerEvent: {}
[0.704283] (firstBot_description) CommandEnded: {'returncode': 0}
[0.705106] (firstBot_description) JobProgress: {'identifier': 'firstBot_description', 'progress': 'build'}
[0.705127] (firstBot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_shooter_controller/lib:/home/<USER>/ros2_ws/install/tide_hw_interface/lib:/home/<USER>/ros2_ws/install/tide_gimbal_controller/lib:/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/ros2_ws/install/serial/lib:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '22669', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '22948', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '81133', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:19998', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/22900,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/22900', 'INVOCATION_ID': 'e3211993af754f99a549a41cea4dd4dc', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc0a30c27ec781a6.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/28d5bc7512b4dc00f1da7b1e392b8057/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.MIGMD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-23bb6a8821.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.712620] (my_gazebo_interface) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.737205] (my_gazebo_interface) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.741327] (firstBot_description) CommandEnded: {'returncode': 0}
[0.741766] (firstBot_description) JobProgress: {'identifier': 'firstBot_description', 'progress': 'install'}
[0.748970] (firstBot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description'], 'cwd': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_shooter_controller/lib:/home/<USER>/ros2_ws/install/tide_hw_interface/lib:/home/<USER>/ros2_ws/install/tide_gimbal_controller/lib:/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/ros2_ws/install/serial/lib:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '22669', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '22948', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '81133', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:19998', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/22900,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/22900', 'INVOCATION_ID': 'e3211993af754f99a549a41cea4dd4dc', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc0a30c27ec781a6.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/28d5bc7512b4dc00f1da7b1e392b8057/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.MIGMD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-23bb6a8821.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.755397] (firstBot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.755493] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch\n'}
[0.755591] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/display.launch.py\n'}
[0.755743] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/gazebo_try.launch.py\n'}
[0.755927] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf\n'}
[0.756076] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot\n'}
[0.756170] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base\n'}
[0.756310] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/base.urdf.xacro\n'}
[0.756469] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/gimbal_base.urdf.xacro\n'}
[0.756593] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.urdf.xacro\n'}
[0.756783] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/sensor\n'}
[0.756938] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins\n'}
[0.757014] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_control_plugins.xacro\n'}
[0.757146] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_sensor_plugins.xacro\n'}
[0.757215] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor\n'}
[0.757296] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal\n'}
[0.757347] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_rotor.urdf.xacro\n'}
[0.757463] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_BarrelWithDials.urdf.xacro\n'}
[0.757616] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_adapter.urdf.xacro\n'}
[0.758127] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_SwingArm.urdf.xacro\n'}
[0.758277] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_ConnectRod.urdf.xacro\n'}
[0.758425] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_base.urdf.xacro\n'}
[0.758573] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/wheel.urdf.xacro\n'}
[0.758733] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/common_inertia.urdf.xacro\n'}
[0.758943] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.ros2_control.xacro\n'}
[0.759166] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config\n'}
[0.759244] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config/firstBot_ros2_controller.yaml\n'}
[0.759464] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/package_run_dependencies/firstBot_description\n'}
[0.759583] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/parent_prefix_path/firstBot_description\n'}
[0.759783] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.sh\n'}
[0.759872] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.dsv\n'}
[0.759965] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.sh\n'}
[0.760055] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.dsv\n'}
[0.760149] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.bash\n'}
[0.760236] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.sh\n'}
[0.760327] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.zsh\n'}
[0.760411] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.dsv\n'}
[0.760500] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.dsv\n'}
[0.760613] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/packages/firstBot_description\n'}
[0.760727] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig.cmake\n'}
[0.760812] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig-version.cmake\n'}
[0.760895] (firstBot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.xml\n'}
[0.762372] (firstBot_description) CommandEnded: {'returncode': 0}
[0.774655] (firstBot_description) JobEnded: {'identifier': 'firstBot_description', 'rc': 0}
[0.800440] (-) TimerEvent: {}
[0.900649] (-) TimerEvent: {}
[0.949067] (my_gazebo_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2\n'}
[1.000769] (-) TimerEvent: {}
[1.004496] (my_gazebo_interface) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[1.010906] (my_gazebo_interface) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[1.041980] (my_gazebo_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2\n'}
[1.100877] (-) TimerEvent: {}
[1.104395] (my_gazebo_interface) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  \n'}
[1.106879] (my_gazebo_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2\n'}
[1.121005] (my_gazebo_interface) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") \n'}
[1.123076] (my_gazebo_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2\n'}
[1.128203] (my_gazebo_interface) StdoutLine: {'line': b'-- Found TinyXML2 via Config file: /usr/lib/x86_64-linux-gnu/cmake/tinyxml2\n'}
[1.137799] (my_gazebo_interface) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.200986] (-) TimerEvent: {}
[1.301221] (-) TimerEvent: {}
[1.401444] (-) TimerEvent: {}
[1.406589] (my_gazebo_interface) StdoutLine: {'line': b'-- Found controller_interface: 4.35.0 (/opt/ros/jazzy/share/controller_interface/cmake)\n'}
[1.420814] (my_gazebo_interface) StderrLine: {'line': b'\x1b[31mCMake Error at CMakeLists.txt:17 (find_package):\n'}
[1.421021] (my_gazebo_interface) StderrLine: {'line': b'  By not providing "Findgazebo_ros2_control.cmake" in CMAKE_MODULE_PATH this\n'}
[1.421077] (my_gazebo_interface) StderrLine: {'line': b'  project has asked CMake to find a package configuration file provided by\n'}
[1.421125] (my_gazebo_interface) StderrLine: {'line': b'  "gazebo_ros2_control", but CMake did not find one.\n'}
[1.421174] (my_gazebo_interface) StderrLine: {'line': b'\n'}
[1.421218] (my_gazebo_interface) StderrLine: {'line': b'  Could not find a package configuration file provided by\n'}
[1.421261] (my_gazebo_interface) StderrLine: {'line': b'  "gazebo_ros2_control" with any of the following names:\n'}
[1.421302] (my_gazebo_interface) StderrLine: {'line': b'\n'}
[1.421344] (my_gazebo_interface) StderrLine: {'line': b'    gazebo_ros2_controlConfig.cmake\n'}
[1.421384] (my_gazebo_interface) StderrLine: {'line': b'    gazebo_ros2_control-config.cmake\n'}
[1.421424] (my_gazebo_interface) StderrLine: {'line': b'\n'}
[1.421467] (my_gazebo_interface) StderrLine: {'line': b'  Add the installation prefix of "gazebo_ros2_control" to CMAKE_PREFIX_PATH\n'}
[1.421508] (my_gazebo_interface) StderrLine: {'line': b'  or set "gazebo_ros2_control_DIR" to a directory containing one of the above\n'}
[1.421549] (my_gazebo_interface) StderrLine: {'line': b'  files.  If "gazebo_ros2_control" provides a separate development package or\n'}
[1.421592] (my_gazebo_interface) StderrLine: {'line': b'  SDK, be sure it has been installed.\n'}
[1.421633] (my_gazebo_interface) StderrLine: {'line': b'\n'}
[1.421674] (my_gazebo_interface) StderrLine: {'line': b'\x1b[0m\n'}
[1.421715] (my_gazebo_interface) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[1.431138] (my_gazebo_interface) CommandEnded: {'returncode': 1}
[1.443341] (my_gazebo_interface) JobEnded: {'identifier': 'my_gazebo_interface', 'rc': 1}
[1.453637] (-) EventReactorShutdown: {}
