-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
-- Install configuration: ""
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/display.launch.py
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/gazebo_try.launch.py
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/base.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/gimbal_base.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/sensor
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_control_plugins.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_sensor_plugins.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_rotor.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_BarrelWithDials.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_adapter.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_SwingArm.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_ConnectRod.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_base.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/wheel.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/common_inertia.urdf.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.ros2_control.xacro
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config/firstBot_ros2_controller.yaml
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/package_run_dependencies/firstBot_description
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/parent_prefix_path/firstBot_description
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.sh
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.dsv
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.bash
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.sh
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.zsh
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.dsv
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.dsv
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/packages/firstBot_description
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig.cmake
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig-version.cmake
-- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.xml
