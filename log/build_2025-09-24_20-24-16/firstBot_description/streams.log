[0.011s] Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description
[0.088s] -- The C compiler identification is GNU 13.3.0
[0.141s] -- The CXX compiler identification is GNU 13.3.0
[0.156s] -- Detecting C compiler ABI info
[0.225s] -- Detecting C compiler ABI info - done
[0.230s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.230s] -- Detecting C compile features
[0.231s] -- Detecting C compile features - done
[0.239s] -- Detecting CXX compiler ABI info
[0.315s] -- Detecting CXX compiler ABI info - done
[0.321s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.321s] -- Detecting CXX compile features
[0.321s] -- Detecting CXX compile features - done
[0.331s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.475s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.596s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.643s] WARNING: Package name "firstBot_description" does not follow the naming conventions. It should start with a lower case letter and only contain lower case letters, digits, underscores, and dashes.
[0.689s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.689s] -- Configured 'flake8' exclude dirs and/or files: 
[0.690s] -- Added test 'lint_cmake' to check CMake code style
[0.691s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.694s] -- Added test 'xmllint' to check XML markup files
[0.696s] -- Configuring done (0.7s)
[0.698s] -- Generating done (0.0s)
[0.700s] -- Build files have been written to: /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
[0.704s] Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/FirstBot(1)/FirstBot/src/firstBot_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description
[0.705s] Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description -- -j32 -l32
[0.741s] Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description -- -j32 -l32
[0.749s] Invoking command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
[0.755s] -- Install configuration: ""
[0.755s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch
[0.755s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/display.launch.py
[0.755s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/launch/gazebo_try.launch.py
[0.755s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/base.urdf.xacro
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/base/gimbal_base.urdf.xacro
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.urdf.xacro
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/sensor
[0.756s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_control_plugins.xacro
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/plugins/gazebo_sensor_plugins.xacro
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_rotor.urdf.xacro
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_BarrelWithDials.urdf.xacro
[0.757s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_adapter.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_SwingArm.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_ConnectRod.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/gimbal/gimbal_pitch_base.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/actutor/wheel.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/common_inertia.urdf.xacro
[0.758s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/urdf/firstBot/firstBot.ros2_control.xacro
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/config/firstBot_ros2_controller.yaml
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/package_run_dependencies/firstBot_description
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/parent_prefix_path/firstBot_description
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.sh
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/ament_prefix_path.dsv
[0.759s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.sh
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/environment/path.dsv
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.bash
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.sh
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.zsh
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/local_setup.dsv
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.dsv
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/ament_index/resource_index/packages/firstBot_description
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig.cmake
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/cmake/firstBot_descriptionConfig-version.cmake
[0.760s] -- Installing: /home/<USER>/ros2_ws/src/FirstBot(1)/install/firstBot_description/share/firstBot_description/package.xml
[0.762s] Invoked command in '/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
