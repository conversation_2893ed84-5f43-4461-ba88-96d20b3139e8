AMENT_PREFIX_PATH=/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy
BUNDLED_DEBUGPY_PATH=/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy
CHROME_DESKTOP=code.desktop
CLUTTER_DISABLE_MIPMAPPED_TEXT=1
CMAKE_PREFIX_PATH=/home/<USER>/ros2_ws/install/tide_ctrl_bringup:/home/<USER>/ros2_ws/install/tide_shooter_controller:/home/<USER>/ros2_ws/install/tide_robot_description:/home/<USER>/ros2_ws/install/tide_hw_interface:/home/<USER>/ros2_ws/install/tide_gimbal_controller:/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/ros2_ws/install/serial:/home/<USER>/ros2_ws/install/rplidar_ros:/home/<USER>/ros2_ws/install/roscon2022_control_workshop:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs:/home/<USER>/ros2_ws/install/pkg_gazebo_1:/home/<USER>/ros2_ws/install/pkg_gazebo:/home/<USER>/ros2_ws/install/controlko_bringup:/home/<USER>/ros2_ws/install/controlko_description:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor
COLCON=1
COLCON_PREFIX_PATH=/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install
COLORTERM=truecolor
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DEBUGINFOD_URLS=https://debuginfod.ubuntu.com
DESKTOP_SESSION=ubuntu
DISPLAY=:0
GDK_BACKEND=x11
GDMSESSION=ubuntu
GIO_LAUNCHED_DESKTOP_FILE=/usr/share/applications/code.desktop
GIO_LAUNCHED_DESKTOP_FILE_PID=81133
GIT_ASKPASS=/usr/share/code/resources/app/extensions/git/dist/askpass.sh
GJS_DEBUG_OUTPUT=stderr
GJS_DEBUG_TOPICS=JS ERROR;JS LOG
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SETUP_DISPLAY=:1
GNOME_SHELL_SESSION_MODE=ubuntu
GSM_SKIP_SSH_AGENT_WORKAROUND=true
GTK_MODULES=gail:atk-bridge
GZ_CONFIG_PATH=/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz
HOME=/home/<USER>
IGN_GAZEBO_RESOURCE_PATH=/opt/ros/jazzy/share
IM_CONFIG_CHECK_ENV=1
IM_CONFIG_PHASE=1
INVOCATION_ID=e3211993af754f99a549a41cea4dd4dc
JOURNAL_STREAM=9:19998
LANG=zh_CN.UTF-8
LANGUAGE=zh_CN:zh_HK:en
LC_ADDRESS=en_US.UTF-8
LC_ALL=en_US.UTF-8
LC_IDENTIFICATION=en_US.UTF-8
LC_MEASUREMENT=en_US.UTF-8
LC_MONETARY=en_US.UTF-8
LC_NAME=en_US.UTF-8
LC_NUMERIC=en_US.UTF-8
LC_PAPER=en_US.UTF-8
LC_TELEPHONE=en_US.UTF-8
LC_TIME=en_US.UTF-8
LD_LIBRARY_PATH=/home/<USER>/ros2_ws/install/tide_shooter_controller/lib:/home/<USER>/ros2_ws/install/tide_hw_interface/lib:/home/<USER>/ros2_ws/install/tide_gimbal_controller/lib:/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/ros2_ws/install/serial/lib:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=ling
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:
MANAGERPID=22669
MEMORY_PRESSURE_WATCH=/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure
MEMORY_PRESSURE_WRITE=c29tZSAyMDAwMDAgMjAwMDAwMAA=
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
PAPERSIZE=letter
PATH=/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin
PWD=/home/<USER>/ros2_ws/src/FirstBot(1)/build/firstBot_description
PYDEVD_DISABLE_FILE_VALIDATION=1
PYTHONPATH=/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages
PYTHONSTARTUP=/home/<USER>/.config/Code/User/workspaceStorage/28d5bc7512b4dc00f1da7b1e392b8057/ms-python.python/pythonrc.py
PYTHON_BASIC_REPL=1
QT_ACCESSIBILITY=1
QT_IM_MODULE=ibus
ROS_AUTOMATIC_DISCOVERY_RANGE=SUBNET
ROS_DISTRO=jazzy
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SESSION_MANAGER=local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/22900,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/22900
SHELL=/bin/bash
SHLVL=1
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSTEMD_EXEC_PID=22948
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.101.0
USER=ling
USERNAME=ling
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS=/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc0a30c27ec781a6.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/usr/share/code/resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/usr/share/code/code
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-23bb6a8821.sock
WAYLAND_DISPLAY=wayland-0
XAUTHORITY=/run/user/1000/.mutter-Xwaylandauth.MIGMD3
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=Unity
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=wayland
XMODIFIERS=@im=ibus
_=/usr/bin/colcon
