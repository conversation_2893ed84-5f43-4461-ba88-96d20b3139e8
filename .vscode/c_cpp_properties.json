{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/ros2_ws/install/tide_shooter_controller/include/**", "/home/<USER>/ros2_ws/install/tide_hw_interface/include/**", "/home/<USER>/ros2_ws/install/tide_gimbal_controller/include/**", "/home/<USER>/ros2_ws/install/tide_msgs/include/**", "/home/<USER>/ros2_ws/install/serial/include/**", "/home/<USER>/ros2_ws/install/ros2_socketcan_msgs/include/**", "/home/<USER>/dev_ws/install/learning_interface/include/**", "/opt/ros/jazzy/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}